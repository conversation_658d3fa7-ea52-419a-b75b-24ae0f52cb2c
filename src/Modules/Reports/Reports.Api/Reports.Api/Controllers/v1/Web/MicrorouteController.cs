using System.Net;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.OpenApi;
using Microsoft.AspNetCore.Mvc;
using Orion.SharedKernel.Api.Controllers;
using Orion.SharedKernel.Domain.Entities.Error;
using Reports.Application.Features.Web.Queries.Microroutes.GetAllRegulatoryRouteCodes;

namespace Reports.Api.Controllers.v1.Web;

[ApiVersion("1.0")]
[Authorize]
public class MicrorouteController : OrionController
{
    [EndpointSummary("Obtiene códigos de ruta regulatorios")]
    [EndpointDescription("Obtiene la lista completa de códigos de ruta regulatorios de la superintendencia.")]
    [ProducesResponseType((int)HttpStatusCode.OK, Type = typeof(List<GetAllRegulatoryRouteCodesResponse>))]
    [ProducesResponseType((int)HttpStatusCode.NoContent, Type = typeof(Error))]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpGet("regulatory-routes")]
    public async Task<IActionResult> GetAll()
    {
        var request = new GetAllRegulatoryRouteCodesRequest();

        var response = await Mediator.Send(request);

        return Ok(response.RegulatoryRouteCodes);
    }
}