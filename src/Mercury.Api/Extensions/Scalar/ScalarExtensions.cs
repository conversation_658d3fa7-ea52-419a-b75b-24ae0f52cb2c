using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Scalar.AspNetCore;

namespace Mercury.Api.Extensions;

public static class ScalarExtensions
{
    public static IServiceCollection AddScalarApi(
        this IServiceCollection services)
    {
        services.AddHttpContextAccessor();
        services.AddVersioning();
        services.AddOpenApiVersioning();
        return services;
    }

    private static IServiceCollection AddVersioning(this IServiceCollection services)
    {
        services.AddApiVersioning(setup =>
        {
            setup.DefaultApiVersion = new ApiVersion(1, 0);
            setup.AssumeDefaultVersionWhenUnspecified = true;
            setup.ReportApiVersions = true;
        });
        
        services.AddVersionedApiExplorer(setup =>
        {
            setup.GroupNameFormat = "'v'VVV";
            setup.SubstituteApiVersionInUrl = true;
        });
        
        return services;
    }

    private static IServiceCollection AddOpenApiVersioning(
        this IServiceCollection services)
    {
        services.AddOpenApi(options =>
        {
            options.AddDocumentTransformer<AuthSecuritySchemeTransformer>();   
            
            options.AddDocumentTransformer<ApiVersionDocumentTransformer>();
        });

        return services;
    }

    public static IApplicationBuilder UseScalarWithVersioning(
        this IApplicationBuilder app,
        WebApplication webApp)
    {
        var provider = app.ApplicationServices.GetRequiredService<IApiVersionDescriptionProvider>();
        
        foreach (var description in provider.ApiVersionDescriptions)
        {
            var groupName = description.GroupName;
            var routePattern = webApp.Environment.IsProduction() 
                ? $"/api/openapi/{groupName}"
                : $"/openapi/{groupName}";

            webApp.MapOpenApi(routePattern);
        }

        foreach (var description in provider.ApiVersionDescriptions)
        {
            var groupName = description.GroupName;
            var openApiPath = webApp.Environment.IsProduction() 
                ? $"/api/openapi/{groupName}"
                : $"/openapi/{groupName}";
                
            webApp.MapScalarApiReference($"/scalar/{groupName}", options =>
            {
                options
                    .WithTitle($"Mercury API {groupName.ToUpperInvariant()}")
                    .WithTheme(ScalarTheme.Purple)
                    .WithPersistentAuthentication()
                    .WithDefaultHttpClient(ScalarTarget.CSharp, ScalarClient.HttpClient)
                    .WithOpenApiRoutePattern(openApiPath);
            }).WithName($"scalar-{groupName}");
        }

        var latestVersion = provider.ApiVersionDescriptions
            .OrderByDescending(x => x.ApiVersion)
            .FirstOrDefault();
            
        if (latestVersion != null)
        {
            var defaultOpenApiPath = webApp.Environment.IsProduction() 
                ? $"/api/openapi/{latestVersion.GroupName}"
                : $"/openapi/{latestVersion.GroupName}";
                
            webApp.MapScalarApiReference("/scalar", options =>
            {
                options
                    .WithTheme(ScalarTheme.Purple)
                    .WithPersistentAuthentication()
                    .WithDefaultHttpClient(ScalarTarget.Http, ScalarClient.HttpClient)
                    .WithOpenApiRoutePattern(defaultOpenApiPath);
            }).WithName("scalar-default");
        }

        return app;
    }
}